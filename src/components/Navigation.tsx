// src/components/Navigation.tsx
'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import AppStoreButtons from './ui/AppStoreButtons';

const Navigation: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <>
      <nav className="fixed top-10 left-0 right-0 z-50 bg-transparent w-full">
        <div className="container mx-auto px-4">
          {/* Desktop Navigation - Hidden on mobile and tablet */}
          <div className="hidden lg:flex justify-between items-center">
            {/* Left side */}
            <div className="flex items-center gap-10">
              <Link
                href="/notre-mission"
                className="bg-[#FFE569] text-noirChaud px-6 py-3 rounded-full font-saint-regus font-bold"
              >
                NOTRE MISSION
              </Link>
              <Link
                href="/fonctionnement"
                className="bg-[#320606] text-[#FFE569] px-6 py-3 rounded-full font-saint-regus font-bold"
              >
                FONCTIONNEMENT
              </Link>
            </div>

            {/* Center - Logo */}
            <Link href="/" className="absolute left-1/2 transform -translate-x-1/2">
              <Image
                src="/images/LOGO-COMPLET-BICOLORE.svg"
                alt="Pepit Logo"
                width={150}
                height={40}
              />
            </Link>

            {/* Right side */}
            <div className="flex items-center gap-20">
              <Link
                href="/commercants"
                className="bg-[#FFE569] text-noirChaud px-6 py-3 rounded-full font-saint-regus font-bold"
              >
                COMMERÇANTS
              </Link>
              <AppStoreButtons />
            </div>
          </div>

          {/* Mobile/Tablet Navigation - Visible on mobile and tablet */}
          <div className="flex lg:hidden justify-between items-center">
            {/* Logo */}
            <Link href="/" onClick={closeMobileMenu}>
              <Image
                src="/images/LOGO-COMPLET-BICOLORE.svg"
                alt="Pepit Logo"
                width={120}
                height={32}
              />
            </Link>

            {/* Hamburger Menu Button */}
            <button
              onClick={toggleMobileMenu}
              className="flex flex-col justify-center items-center w-8 h-8 space-y-1.5 focus:outline-none"
              aria-label="Toggle mobile menu"
            >
              <span
                className={`block w-6 h-0.5 bg-[#320606] transition-all duration-300 ${
                  isMobileMenuOpen ? 'rotate-45 translate-y-2' : ''
                }`}
              />
              <span
                className={`block w-6 h-0.5 bg-[#320606] transition-all duration-300 ${
                  isMobileMenuOpen ? 'opacity-0' : ''
                }`}
              />
              <span
                className={`block w-6 h-0.5 bg-[#320606] transition-all duration-300 ${
                  isMobileMenuOpen ? '-rotate-45 -translate-y-2' : ''
                }`}
              />
            </button>
          </div>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 z-40 lg:hidden">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
            onClick={closeMobileMenu}
          />

          {/* Menu Panel */}
          <div className="fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white shadow-xl transform transition-transform duration-300 ease-in-out">
            <div className="flex flex-col h-full">
              {/* Header */}
              <div className="flex justify-between items-center p-6 border-b border-gray-200">
                <Image
                  src="/images/LOGO-COMPLET-BICOLORE.svg"
                  alt="Pepit Logo"
                  width={120}
                  height={32}
                />
                <button
                  onClick={closeMobileMenu}
                  className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                  aria-label="Close menu"
                >
                  <svg className="w-6 h-6 text-[#320606]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Navigation Links */}
              <div className="flex-1 px-6 py-8 space-y-6">
                <Link
                  href="/notre-mission"
                  onClick={closeMobileMenu}
                  className="block w-full text-center bg-[#FFE569] text-noirChaud px-6 py-4 rounded-full font-saint-regus font-bold text-sm transition-all duration-200 hover:bg-[#ffd700]"
                >
                  NOTRE MISSION
                </Link>
                <Link
                  href="/fonctionnement"
                  onClick={closeMobileMenu}
                  className="block w-full text-center bg-[#320606] text-[#FFE569] px-6 py-4 rounded-full font-saint-regus font-bold text-sm transition-all duration-200 hover:bg-[#4a0a0a]"
                >
                  FONCTIONNEMENT
                </Link>
                <Link
                  href="/commercants"
                  onClick={closeMobileMenu}
                  className="block w-full text-center bg-[#FFE569] text-noirChaud px-6 py-4 rounded-full font-saint-regus font-bold text-sm transition-all duration-200 hover:bg-[#ffd700]"
                >
                  COMMERÇANTS
                </Link>
              </div>

              {/* App Store Buttons */}
              <div className="px-6 pb-8 border-t border-gray-200 pt-6">
                <div className="flex justify-center">
                  <AppStoreButtons />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Navigation;